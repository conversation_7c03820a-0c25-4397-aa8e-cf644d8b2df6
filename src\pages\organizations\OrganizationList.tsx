import { useState } from 'react'
import { Plus } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { DataTable } from '@/components/tables/DataTable'
import { OrganizationForm } from '@/components/forms/OrganizationForm'
import { OrganizationDetail } from './OrganizationDetail'
import { OrganizationSearchFilter } from '@/components/search/OrganizationSearchFilter'
import { useOrganizations, useCreateOrganization, useUpdateOrganization, useDeleteOrganization } from '@/hooks/useOrganizations'
import { formatDate } from '@/lib/utils'

// Temporary type definitions until import issues are fixed
interface IOrganization {
  _id: string;
  name: string;
  address?: string;
  domain: string;
  createdAt: Date | string;
  updatedAt: Date | string;
}

interface OrganizationFormData {
  name: string;
  domain: string;
  address?: string;
}

export function OrganizationList() {
  const { data: organizations, isLoading } = useOrganizations()
  const createMutation = useCreateOrganization()
  const updateMutation = useUpdateOrganization()
  const deleteMutation = useDeleteOrganization()

  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [editingOrganization, setEditingOrganization] = useState<IOrganization | null>(null)
  const [viewingOrganizationId, setViewingOrganizationId] = useState<string | null>(null)
  const [filteredOrganizations, setFilteredOrganizations] = useState<IOrganization[] | null>(null)

  // Use filtered organizations if available, otherwise use all organizations
  const displayOrganizations = filteredOrganizations || organizations || []

  const columns = [
    { key: 'name', label: 'Name', sortable: true },
    { key: 'domain', label: 'Domain', sortable: true },
    {
      key: 'address',
      label: 'Address',
      sortable: false,
      render: (value: string) => value || 'Not provided'
    },
    {
      key: 'createdAt',
      label: 'Created',
      sortable: true,
      render: (value: string) => formatDate(value)
    },
  ]

  const handleCreate = async (data: OrganizationFormData) => {
    await createMutation.mutateAsync(data)
    setIsCreateDialogOpen(false)
  }

  const handleUpdate = async (data: OrganizationFormData) => {
    if (editingOrganization) {
      await updateMutation.mutateAsync({
        id: editingOrganization._id.toString(),
        data,
      })
      setEditingOrganization(null)
    }
  }

  const handleDelete = async (id: string) => {
    if (confirm('Are you sure you want to delete this organization?')) {
      await deleteMutation.mutateAsync(id)
    }
  }

  const handleEdit = (id: string) => {
    const org = displayOrganizations.find(o => o._id.toString() === id)
    if (org) {
      setEditingOrganization(org)
    }
  }

  const handleView = (id: string) => {
    setViewingOrganizationId(id)
  }

  // Show detail view if viewing an organization
  if (viewingOrganizationId) {
    return (
      <OrganizationDetail
        organizationId={viewingOrganizationId}
        onBack={() => setViewingOrganizationId(null)}
        onDelete={() => setViewingOrganizationId(null)}
      />
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Organizations</h1>
          <p className="text-gray-600">Manage your organizations</p>
        </div>
        
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2">
            <Plus className="h-4 w-4 mr-2" />
            Add Organization
          </DialogTrigger>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Create New Organization</DialogTitle>
            </DialogHeader>
            <OrganizationForm
              mode="create"
              onSubmit={handleCreate}
              onCancel={() => setIsCreateDialogOpen(false)}
              isLoading={createMutation.isPending}
            />
          </DialogContent>
        </Dialog>
      </div>

      {/* Search and Filter */}
      <OrganizationSearchFilter
        onResultsChange={setFilteredOrganizations}
        className="bg-white p-4 rounded-lg shadow-sm mb-6"
      />

      <DataTable
        data={displayOrganizations}
        columns={columns}
        loading={isLoading}
        onEdit={handleEdit}
        onDelete={handleDelete}
        onView={handleView}
      />

      {/* Edit Dialog */}
      <Dialog open={!!editingOrganization} onOpenChange={() => setEditingOrganization(null)}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Edit Organization</DialogTitle>
          </DialogHeader>
          {editingOrganization && (
            <OrganizationForm
              mode="edit"
              initialData={editingOrganization}
              onSubmit={handleUpdate}
              onCancel={() => setEditingOrganization(null)}
              isLoading={updateMutation.isPending}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}