import { Routes, Route, Navigate } from 'react-router-dom'
import { AuthProvider } from '@/contexts/AuthContext'
import { ProtectedRoute } from '@/components/auth/ProtectedRoute'
import { <PERSON><PERSON>r<PERSON>and<PERSON> } from '@/components/ErrorHandler'
import { MainLayout } from '@/components/layout/MainLayout'
import { LoginPage } from '@/pages/auth/LoginPage'
import { Dashboard } from '@/pages/Dashboard'
import { OrganizationList } from '@/pages/organizations/OrganizationList'
import { RecruiterList } from '@/pages/recruiters/RecruiterList'

import { JobList } from '@/pages/jobs/JobList'
import { CandidateList } from '@/pages/candidates/CandidateList'
import { WorkflowList } from '@/pages/workflows/WorkflowList'
import { WorkflowEditor } from '@/pages/workflows/WorkflowEditor'

import { AssessmentList } from '@/pages/assessments/AssessmentList'
import { Toaster } from '@/components/ui/toast'
import './App.css'

function App() {
  return (
    <AuthProvider>
      <ErrorHandler />
      <Routes>
        <Route path="/login" element={<LoginPage />} />
        <Route path="/" element={
          <ProtectedRoute>
            <MainLayout />
          </ProtectedRoute>
        }>
          <Route index element={<Dashboard />} />
          <Route path="organizations" element={<OrganizationList />} />
          <Route path="recruiters" element={<RecruiterList />} />

          <Route path="jobs" element={<JobList />} />
          <Route path="candidates" element={<CandidateList />} />
          <Route path="workflows" element={<WorkflowList />} />
          <Route path="workflows/editor" element={<WorkflowEditor />} />
          <Route path="assessments" element={<AssessmentList />} />
          <Route path="settings" element={<div className="p-6">Settings page coming soon...</div>} />
        </Route>
        <Route path="*" element={<Navigate to="/" replace />} />
      </Routes>
      <Toaster />
    </AuthProvider>
  )
}

export default App
