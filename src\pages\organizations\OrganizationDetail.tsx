import { useState } from 'react'
import { ArrowLeft, Edit, Trash2, Building2, Globe, MapPin, Calendar, Users, Briefcase } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { OrganizationForm } from '@/components/forms/OrganizationForm'
import { useOrganizations, useUpdateOrganization, useDeleteOrganization } from '@/hooks/useOrganizations'
import { formatDate } from '@/lib/utils'

// Temporary type definitions until import issues are fixed
interface IOrganization {
  _id: string;
  name: string;
  address?: string;
  domain: string;
  createdAt: Date | string;
  updatedAt: Date | string;
}

interface OrganizationFormData {
  name: string;
  domain: string;
  address?: string;
}

interface OrganizationDetailProps {
  organizationId: string
  onBack: () => void
  onEdit?: (organization: IOrganization) => void
  onDelete?: (id: string) => void
}

export function OrganizationDetail({ 
  organizationId, 
  onBack, 
  onEdit, 
  onDelete 
}: OrganizationDetailProps) {
  const { data: organizations } = useOrganizations()
  const updateMutation = useUpdateOrganization()
  const deleteMutation = useDeleteOrganization()
  
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  
  const organization = organizations?.find(org => org._id === organizationId)

  if (!organization) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" onClick={onBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Organizations
          </Button>
        </div>
        <div className="text-center py-12">
          <Building2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Organization not found</h3>
          <p className="text-gray-500">The organization you're looking for doesn't exist.</p>
        </div>
      </div>
    )
  }

  const handleUpdate = async (data: OrganizationFormData) => {
    await updateMutation.mutateAsync({
      id: organization._id,
      data,
    })
    setIsEditDialogOpen(false)
  }

  const handleDelete = async () => {
    if (confirm('Are you sure you want to delete this organization? This action cannot be undone.')) {
      await deleteMutation.mutateAsync(organization._id)
      onDelete?.(organization._id)
      onBack()
    }
  }

  // Mock data for related information
  const mockStats = {
    recruiters: 5,
    activeJobs: 12,
    totalCandidates: 89,
    hiredThisMonth: 3
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" onClick={onBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Organizations
          </Button>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => setIsEditDialogOpen(true)}
          >
            <Edit className="h-4 w-4 mr-2" />
            Edit
          </Button>
          <Button
            variant="outline"
            onClick={handleDelete}
            className="text-red-600 hover:text-red-700"
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Delete
          </Button>
        </div>
      </div>

      {/* Organization Info */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Info */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Building2 className="h-6 w-6 text-blue-600" />
                </div>
                <div>
                  <CardTitle className="text-2xl">{organization.name}</CardTitle>
                  <CardDescription>Organization Details</CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="flex items-center space-x-3">
                  <Globe className="h-5 w-5 text-gray-400" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">Domain</p>
                    <p className="text-sm text-gray-600">{organization.domain}</p>
                  </div>
                </div>
                
                {organization.address && (
                  <div className="flex items-center space-x-3">
                    <MapPin className="h-5 w-5 text-gray-400" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">Address</p>
                      <p className="text-sm text-gray-600">{organization.address}</p>
                    </div>
                  </div>
                )}
                
                <div className="flex items-center space-x-3">
                  <Calendar className="h-5 w-5 text-gray-400" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">Created</p>
                    <p className="text-sm text-gray-600">{formatDate(organization.createdAt)}</p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-3">
                  <Calendar className="h-5 w-5 text-gray-400" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">Last Updated</p>
                    <p className="text-sm text-gray-600">{formatDate(organization.updatedAt)}</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Stats */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Quick Stats</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Users className="h-4 w-4 text-gray-400" />
                  <span className="text-sm text-gray-600">Recruiters</span>
                </div>
                <span className="font-semibold">{mockStats.recruiters}</span>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Briefcase className="h-4 w-4 text-gray-400" />
                  <span className="text-sm text-gray-600">Active Jobs</span>
                </div>
                <span className="font-semibold">{mockStats.activeJobs}</span>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Users className="h-4 w-4 text-gray-400" />
                  <span className="text-sm text-gray-600">Total Candidates</span>
                </div>
                <span className="font-semibold">{mockStats.totalCandidates}</span>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Users className="h-4 w-4 text-green-500" />
                  <span className="text-sm text-gray-600">Hired This Month</span>
                </div>
                <span className="font-semibold text-green-600">{mockStats.hiredThisMonth}</span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Recent Activity</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3 text-sm">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span className="text-gray-600">New recruiter added</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-gray-600">Job posting published</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                  <span className="text-gray-600">Profile updated</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Edit Organization</DialogTitle>
          </DialogHeader>
          <OrganizationForm
            mode="edit"
            initialData={organization}
            onSubmit={handleUpdate}
            onCancel={() => setIsEditDialogOpen(false)}
            isLoading={updateMutation.isPending}
          />
        </DialogContent>
      </Dialog>
    </div>
  )
}
