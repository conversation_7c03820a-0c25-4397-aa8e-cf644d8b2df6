import React, { useState, useCallback } from 'react'
import { Upload, FileText, AlertCircle, CheckCircle, X } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { useUploadCSVCandidates, useDownloadCSVTemplate } from '@/hooks/useCandidates'
import { useToast } from '@/hooks/use-toast'
import type { CSVCandidateData, CSVUploadResponse } from '@/types'

interface CSVUploadProps {
  isOpen: boolean
  onClose: () => void
  onSuccess?: (result: CSVUploadResponse) => void
}

export function CSVUpload({ isOpen, onClose, onSuccess }: CSVUploadProps) {
  const [dragActive, setDragActive] = useState(false)
  const [file, setFile] = useState<File | null>(null)
  const [csvData, setCsvData] = useState<CSVCandidateData[]>([])
  const [uploadResult, setUploadResult] = useState<CSVUploadResponse | null>(null)
  const [step, setStep] = useState<'upload' | 'preview' | 'result'>('upload')

  const uploadMutation = useUploadCSVCandidates()
  const downloadTemplateMutation = useDownloadCSVTemplate()
  const { toast } = useToast()

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true)
    } else if (e.type === 'dragleave') {
      setDragActive(false)
    }
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)

    const files = Array.from(e.dataTransfer.files)
    if (files.length > 0) {
      handleFileSelect(files[0])
    }
  }, [])

  const handleFileSelect = (selectedFile: File) => {
    if (!selectedFile.name.endsWith('.csv')) {
      toast({
        title: 'Invalid file type',
        description: 'Please select a CSV file.',
        variant: 'destructive',
      })
      return
    }

    if (selectedFile.size > 10 * 1024 * 1024) { // 10MB limit
      toast({
        title: 'File too large',
        description: 'Please select a file smaller than 10MB.',
        variant: 'destructive',
      })
      return
    }

    setFile(selectedFile)
    parseCSV(selectedFile)
  }

  const parseCSV = (file: File) => {
    const reader = new FileReader()
    reader.onload = (e) => {
      const text = e.target?.result as string
      const lines = text.split('\n').filter(line => line.trim())
      
      if (lines.length < 2) {
        toast({
          title: 'Invalid CSV',
          description: 'CSV file must have at least a header row and one data row.',
          variant: 'destructive',
        })
        return
      }

      const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''))
      const data: CSVCandidateData[] = []

      for (let i = 1; i < lines.length; i++) {
        const values = lines[i].split(',').map(v => v.trim().replace(/"/g, ''))
        const row: any = {}
        
        headers.forEach((header, index) => {
          const value = values[index] || ''
          if (header === 'expectedSalary' && value) {
            row[header] = parseFloat(value)
          } else {
            row[header] = value
          }
        })

        if (row.name && row.email) { // Basic validation
          data.push(row as CSVCandidateData)
        }
      }

      setCsvData(data)
      setStep('preview')
    }

    reader.readAsText(file)
  }

  const handleUpload = async () => {
    try {
      const result = await uploadMutation.mutateAsync(csvData)
      setUploadResult(result)
      setStep('result')
      onSuccess?.(result)
      
      toast({
        title: 'Upload completed',
        description: `Successfully processed ${result.summary.successfulInserts} out of ${result.summary.totalRows} candidates.`,
      })
    } catch (error) {
      toast({
        title: 'Upload failed',
        description: 'Failed to upload CSV data. Please try again.',
        variant: 'destructive',
      })
    }
  }

  const handleDownloadTemplate = async () => {
    try {
      await downloadTemplateMutation.mutateAsync()
      toast({
        title: 'Template downloaded',
        description: 'CSV template has been downloaded to your computer.',
      })
    } catch (error) {
      toast({
        title: 'Download failed',
        description: 'Failed to download CSV template. Please try again.',
        variant: 'destructive',
      })
    }
  }

  const resetUpload = () => {
    setFile(null)
    setCsvData([])
    setUploadResult(null)
    setStep('upload')
  }

  const handleClose = () => {
    resetUpload()
    onClose()
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Upload Candidates via CSV</DialogTitle>
        </DialogHeader>

        {step === 'upload' && (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <p className="text-sm text-gray-600">
                Upload a CSV file with candidate data. Download the template to see the required format.
              </p>
              <Button
                variant="outline"
                onClick={handleDownloadTemplate}
                disabled={downloadTemplateMutation.isPending}
              >
                <FileText className="h-4 w-4 mr-2" />
                Download Template
              </Button>
            </div>

            <div
              className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                dragActive
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-300 hover:border-gray-400'
              }`}
              onDragEnter={handleDrag}
              onDragLeave={handleDrag}
              onDragOver={handleDrag}
              onDrop={handleDrop}
            >
              <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-lg font-medium text-gray-900 mb-2">
                Drop your CSV file here, or click to browse
              </p>
              <p className="text-sm text-gray-500 mb-4">
                Supports CSV files up to 10MB
              </p>
              <input
                type="file"
                accept=".csv"
                onChange={(e) => e.target.files?.[0] && handleFileSelect(e.target.files[0])}
                className="hidden"
                id="csv-upload"
                ref={(input) => {
                  if (input) {
                    (window as any).csvFileInput = input
                  }
                }}
              />
              <Button
                variant="outline"
                type="button"
                onClick={() => {
                  const input = document.getElementById('csv-upload') as HTMLInputElement
                  input?.click()
                }}
                className="cursor-pointer"
              >
                Browse Files
              </Button>
            </div>
          </div>
        )}

        {step === 'preview' && (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-medium">Preview CSV Data</h3>
                <p className="text-sm text-gray-600">
                  Found {csvData.length} candidates in the CSV file
                </p>
              </div>
              <div className="space-x-2">
                <Button variant="outline" onClick={resetUpload}>
                  <X className="h-4 w-4 mr-2" />
                  Cancel
                </Button>
                <Button 
                  onClick={handleUpload}
                  disabled={uploadMutation.isPending}
                >
                  {uploadMutation.isPending ? 'Uploading...' : 'Upload Candidates'}
                </Button>
              </div>
            </div>

            <div className="border rounded-lg overflow-hidden">
              <div className="max-h-96 overflow-y-auto">
                <table className="w-full text-sm">
                  <thead className="bg-gray-50 sticky top-0">
                    <tr>
                      <th className="px-4 py-2 text-left">Name</th>
                      <th className="px-4 py-2 text-left">Email</th>
                      <th className="px-4 py-2 text-left">Phone</th>
                      <th className="px-4 py-2 text-left">Job ID</th>
                      <th className="px-4 py-2 text-left">Source</th>
                    </tr>
                  </thead>
                  <tbody>
                    {csvData.slice(0, 10).map((candidate, index) => (
                      <tr key={index} className="border-t">
                        <td className="px-4 py-2">{candidate.name}</td>
                        <td className="px-4 py-2">{candidate.email}</td>
                        <td className="px-4 py-2">{candidate.phone || '-'}</td>
                        <td className="px-4 py-2">{candidate.jobId}</td>
                        <td className="px-4 py-2">{candidate.source}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
                {csvData.length > 10 && (
                  <div className="p-4 text-center text-sm text-gray-500">
                    ... and {csvData.length - 10} more candidates
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {step === 'result' && uploadResult && (
          <div className="space-y-6">
            <div className="text-center">
              <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
              <h3 className="text-lg font-medium">Upload Complete</h3>
            </div>

            <div className="grid grid-cols-3 gap-4">
              <Card>
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {uploadResult.summary.totalRows}
                  </div>
                  <div className="text-sm text-gray-600">Total Rows</div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {uploadResult.summary.successfulInserts}
                  </div>
                  <div className="text-sm text-gray-600">Successful</div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-red-600">
                    {uploadResult.summary.failedRows}
                  </div>
                  <div className="text-sm text-gray-600">Failed</div>
                </CardContent>
              </Card>
            </div>

            {uploadResult.errors.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-red-600 flex items-center">
                    <AlertCircle className="h-5 w-5 mr-2" />
                    Errors ({uploadResult.errors.length})
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2 max-h-48 overflow-y-auto">
                    {uploadResult.errors.map((error, index) => (
                      <div key={index} className="p-3 bg-red-50 rounded border">
                        <div className="font-medium text-sm">Row {error.row}</div>
                        <div className="text-xs text-gray-600 mb-1">
                          {error.data.name} ({error.data.email})
                        </div>
                        <div className="space-y-1">
                          {error.errors.map((err, errIndex) => (
                            <Badge key={errIndex} variant="destructive" className="text-xs">
                              {err}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={resetUpload}>
                Upload Another File
              </Button>
              <Button onClick={handleClose}>
                Close
              </Button>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}
