import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { api } from '@/lib/api'
import { IJobConfig, IStatusConfig, IStageConfig, StageOutputsResponse, OutputMapping } from '@/types'

interface JobConfigFormData {
  jobId: string;
  flow: IStatusConfig[];
  stageConfig: IStageConfig[];
}

// Get all job configurations
export function useJobConfigs() {
  return useQuery({
    queryKey: ['jobconfigs'],
    queryFn: () => api.get<IJobConfig[]>('/job-config'),
  })
}

// Get job configuration by job ID
export function useJobConfig(jobId: string) {
  return useQuery({
    queryKey: ['jobconfigs', jobId],
    queryFn: () => api.get<IJobConfig>(`/job-config/${jobId}`),
    enabled: !!jobId,
  })
}

// Create job configuration
export function useCreateJobConfig() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: JobConfigFormData) =>
      api.post<IJobConfig>('/job-config', data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['jobconfigs'] })
    },
  })
}

// Update job configuration
export function useUpdateJobConfig() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: JobConfigFormData }) =>
      api.put<IJobConfig>(`/job-config/${id}`, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['jobconfigs'] })
    },
  })
}

// Update job configuration stage
export function useUpdateJobConfigStage() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ jobId, stage, data }: { jobId: string; stage: string; data: any }) =>
      api.put<IJobConfig>(`/job-config/${jobId}/stage/${stage}`, data),
    onSuccess: (_, { jobId }) => {
      queryClient.invalidateQueries({ queryKey: ['jobconfigs'] })
      queryClient.invalidateQueries({ queryKey: ['jobconfigs', jobId] })
    },
  })
}

// Delete job configuration
export function useDeleteJobConfig() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (jobId: string) => api.delete(`/job-config/${jobId}`),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['jobconfigs'] })
    },
  })
}

// Get job config analytics
export function useJobConfigAnalytics() {
  return useQuery({
    queryKey: ['jobconfigs', 'analytics'],
    queryFn: () => api.get<{
      totalConfigs: number;
      configsByStatus: Record<string, number>;
    }>('/job-config/analytics'),
  })
}

// New hooks for predefined output management
export function useStageOutputs(jobId: string, stage: string) {
  return useQuery({
    queryKey: ['stage-outputs', jobId, stage],
    queryFn: () => api.get<StageOutputsResponse>(`/job-config/${jobId}/stage/${stage}/outputs`),
    enabled: !!jobId && !!stage,
  })
}

export function useUpdateOutputMappings() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ jobId, stage, outputMappings }: {
      jobId: string;
      stage: string;
      outputMappings: OutputMapping[];
    }) => api.put(`/job-config/${jobId}/stage/${stage}/output-mappings`, { outputMappings }),
    onSuccess: (_, { jobId, stage }) => {
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ['stage-outputs', jobId, stage] })
      queryClient.invalidateQueries({ queryKey: ['jobconfigs', jobId] })
    },
  })
}



// Export types for use in components
export type { JobConfigFormData }
