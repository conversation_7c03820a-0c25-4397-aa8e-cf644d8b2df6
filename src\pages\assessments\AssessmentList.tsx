import React, { useState } from 'react'
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { DataTable } from '@/components/tables/DataTable'
import { AssessmentConfigForm } from '@/components/assessments/AssessmentConfigForm'
import { 
  useAssessments, 
  useCreateAssessment, 
  useUpdateAssessment, 
  useDeleteAssessment, 
  useToggleAssessmentStatus 
} from '@/hooks/useAssessments'
import { useJobs } from '@/hooks/useJobs'
import { 
  Plus, 
  Search, 
  Filter, 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  Power, 
  PowerOff,
  Brain,
  FileText,
  Zap,
  Calendar,
  Building,
  Users
} from 'lucide-react'
import type { IAssessmentConfig, AssessmentType } from '@/types'

export function AssessmentList() {
  const [searchTerm, setSearchTerm] = useState('')
  const [filterJobId, setFilterJobId] = useState<string>('')
  const [filterStage, setFilterStage] = useState<string>('')
  const [filterType, setFilterType] = useState<AssessmentType | ''>('')
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [editingAssessment, setEditingAssessment] = useState<IAssessmentConfig | null>(null)

  // API hooks
  const { data: assessments, isLoading } = useAssessments({
    jobId: filterJobId && filterJobId !== 'all-jobs' ? filterJobId : undefined,
    stage: filterStage || undefined,
    assessmentType: filterType || undefined,
  })
  const { data: jobs } = useJobs()
  const createMutation = useCreateAssessment()
  const updateMutation = useUpdateAssessment()
  const deleteMutation = useDeleteAssessment()
  const toggleStatusMutation = useToggleAssessmentStatus()

  // Filter assessments by search term
  const filteredAssessments = assessments?.assessmentConfigs.filter(assessment => {
    if (!searchTerm) return true
    const searchLower = searchTerm.toLowerCase()
    return (
      assessment.stage.toLowerCase().includes(searchLower) ||
      assessment.assessmentType.toLowerCase().includes(searchLower) ||
      (typeof assessment.jobId === 'object' && assessment.jobId.title?.toLowerCase().includes(searchLower))
    )
  }) || []

  const getAssessmentTypeIcon = (type: AssessmentType) => {
    switch (type) {
      case 'ai-interview':
        return <Brain className="h-4 w-4 text-blue-600" />
      case 'manual':
        return <FileText className="h-4 w-4 text-green-600" />
      case 'hybrid':
        return <Zap className="h-4 w-4 text-purple-600" />
      default:
        return null
    }
  }

  const getAssessmentTypeBadge = (type: AssessmentType) => {
    const colors = {
      'ai-interview': 'bg-blue-100 text-blue-800 border-blue-200',
      'manual': 'bg-green-100 text-green-800 border-green-200',
      'hybrid': 'bg-purple-100 text-purple-800 border-purple-200',
    }
    
    return (
      <Badge className={`${colors[type]} border`}>
        <div className="flex items-center space-x-1">
          {getAssessmentTypeIcon(type)}
          <span>{type.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}</span>
        </div>
      </Badge>
    )
  }

  const formatDate = (date: Date | string) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    })
  }

  const getJobTitle = (jobId: any) => {
    if (typeof jobId === 'object' && jobId.title) {
      return jobId.title
    }
    const job = jobs?.find(j => j._id === jobId)
    return job?.title || 'Unknown Job'
  }

  const handleCreate = (data: any) => {
    createMutation.mutate(data, {
      onSuccess: () => {
        setIsCreateDialogOpen(false)
      }
    })
  }

  const handleUpdate = (data: any) => {
    if (editingAssessment) {
      updateMutation.mutate({
        configId: editingAssessment._id.toString(),
        data
      }, {
        onSuccess: () => {
          setEditingAssessment(null)
        }
      })
    }
  }

  const handleDelete = (configId: string) => {
    if (confirm('Are you sure you want to delete this assessment configuration?')) {
      deleteMutation.mutate(configId)
    }
  }

  const handleToggleStatus = (configId: string) => {
    toggleStatusMutation.mutate(configId)
  }

  const columns = [
    {
      key: 'jobId',
      label: 'Job',
      sortable: true,
      render: (value: any, row: IAssessmentConfig) => (
        <div className="min-w-[200px]">
          <div className="flex items-center space-x-2">
            <Building className="h-4 w-4 text-gray-400" />
            <span className="font-medium text-gray-900">{getJobTitle(value)}</span>
          </div>
          <div className="text-sm text-gray-500 mt-1">
            Stage: {row.stage}
          </div>
        </div>
      )
    },
    {
      key: 'assessmentType',
      label: 'Type',
      sortable: true,
      render: (value: AssessmentType) => (
        <div className="min-w-[140px]">
          {getAssessmentTypeBadge(value)}
        </div>
      )
    },
    {
      key: 'isActive',
      label: 'Status',
      sortable: true,
      render: (value: boolean) => (
        <div className="min-w-[100px]">
          <Badge className={value ? 'bg-green-100 text-green-800 border-green-200' : 'bg-gray-100 text-gray-800 border-gray-200'}>
            {value ? (
              <div className="flex items-center space-x-1">
                <Power className="h-3 w-3" />
                <span>Active</span>
              </div>
            ) : (
              <div className="flex items-center space-x-1">
                <PowerOff className="h-3 w-3" />
                <span>Inactive</span>
              </div>
            )}
          </Badge>
        </div>
      )
    },
    {
      key: 'createdAt',
      label: 'Created',
      sortable: true,
      render: (value: Date) => (
        <div className="min-w-[120px]">
          <div className="flex items-center space-x-1">
            <Calendar className="h-4 w-4 text-gray-400" />
            <span className="text-sm text-gray-600">{formatDate(value)}</span>
          </div>
        </div>
      )
    },
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Assessment Configurations</h1>
          <p className="text-gray-600">Manage assessment configurations for different job stages</p>
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Create Assessment
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Create Assessment Configuration</DialogTitle>
            </DialogHeader>
            <AssessmentConfigForm
              jobId=""
              stage=""
              onSubmit={handleCreate}
              onCancel={() => setIsCreateDialogOpen(false)}
              isLoading={createMutation.isPending}
            />
          </DialogContent>
        </Dialog>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search assessments..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>

            <Select value={filterJobId || undefined} onValueChange={(value) => setFilterJobId(value === 'all-jobs' ? '' : value || '')}>
              <SelectTrigger>
                <SelectValue placeholder="Filter by job" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all-jobs">All Jobs</SelectItem>
                {jobs?.map(job => (
                  <SelectItem key={job._id} value={job._id}>
                    {job.title}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Input
              placeholder="Filter by stage"
              value={filterStage}
              onChange={(e) => setFilterStage(e.target.value)}
            />

            <Select value={filterType || undefined} onValueChange={(value: AssessmentType | 'all-types') => setFilterType(value === 'all-types' ? '' : value as AssessmentType)}>
              <SelectTrigger>
                <SelectValue placeholder="Filter by type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all-types">All Types</SelectItem>
                <SelectItem value="ai-interview">AI Interview</SelectItem>
                <SelectItem value="manual">Manual Assessment</SelectItem>
                <SelectItem value="hybrid">Hybrid Assessment</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Assessments</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{assessments?.total || 0}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active</CardTitle>
            <Power className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {filteredAssessments.filter(a => a.isActive).length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">AI Interviews</CardTitle>
            <Brain className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {filteredAssessments.filter(a => a.assessmentType === 'ai-interview').length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Hybrid</CardTitle>
            <Zap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {filteredAssessments.filter(a => a.assessmentType === 'hybrid').length}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Assessment Table */}
      <div className="bg-white rounded-lg shadow-sm">
        <DataTable
          data={filteredAssessments}
          columns={columns}
          loading={isLoading}
          onEdit={(id) => {
            const assessment = filteredAssessments.find(a => a._id.toString() === id)
            if (assessment) setEditingAssessment(assessment)
          }}
          onDelete={(id) => handleDelete(id)}
          actions={(row) => (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => setEditingAssessment(row)}>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleToggleStatus(row._id.toString())}>
                  {row.isActive ? (
                    <>
                      <PowerOff className="h-4 w-4 mr-2" />
                      Deactivate
                    </>
                  ) : (
                    <>
                      <Power className="h-4 w-4 mr-2" />
                      Activate
                    </>
                  )}
                </DropdownMenuItem>
                <DropdownMenuItem 
                  onClick={() => handleDelete(row._id.toString())}
                  className="text-red-600"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        />
      </div>

      {/* Edit Dialog */}
      <Dialog open={!!editingAssessment} onOpenChange={() => setEditingAssessment(null)}>
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Assessment Configuration</DialogTitle>
          </DialogHeader>
          {editingAssessment && (
            <AssessmentConfigForm
              jobId={editingAssessment.jobId.toString()}
              stage={editingAssessment.stage}
              initialData={editingAssessment}
              onSubmit={handleUpdate}
              onCancel={() => setEditingAssessment(null)}
              isLoading={updateMutation.isPending}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
