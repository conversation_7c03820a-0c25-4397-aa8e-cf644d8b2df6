import { useState, useEffect } from 'react'
import { useSearchParams } from 'react-router-dom'
import { ArrowLeft, <PERSON>ting<PERSON>, Play, Save, AlertTriangle } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { WorkflowCanvas } from '@/components/workflow/WorkflowCanvas'
import { StageConfigPanel } from '@/components/workflow/StageConfigPanel'
import { useWorkflowStore } from '@/stores/workflowStore'
import { useJobs } from '@/hooks/useJobs'
import { useToast } from '@/hooks/use-toast'

export function WorkflowEditor() {
  const [searchParams, setSearchParams] = useSearchParams()
  const jobId = searchParams.get('jobId')
  const [selectedJobId, setSelectedJobId] = useState<string>(jobId || '')

  const { data: jobs, isLoading: jobsLoading } = useJobs()
  const { toast } = useToast()

  const {
    currentJobConfig,
    loadWorkflow,
    createNewWorkflow,
    saveWorkflow,
    validateWorkflow,
    nodes,
    edges
  } = useWorkflowStore()

  // Load workflow when job is selected
  useEffect(() => {
    if (selectedJobId && jobs) {
      const job = jobs.find(j => j._id === selectedJobId)
      if (job) {
        // In a real app, we would fetch the job config from the API
        // For now, we'll create a sample workflow or load from mock data
        const sampleJobConfig = {
          _id: `config_${selectedJobId}`,
          jobId: selectedJobId,
          flow: [
            {
              stage: 'veda-review',
              next: [
                { stage: 'screening', outcome: 'qualified' },
                { stage: 'rejected', outcome: 'not_qualified' }
              ]
            },
            {
              stage: 'screening',
              next: [
                { stage: 'assessment', outcome: 'pass' },
                { stage: 'rejected', outcome: 'fail' }
              ]
            },
            {
              stage: 'assessment',
              next: [
                { stage: 'interview', outcome: 'pass' },
                { stage: 'rejected', outcome: 'fail' }
              ]
            },
            {
              stage: 'interview',
              next: [
                { stage: 'offer', outcome: 'hire' },
                { stage: 'rejected', outcome: 'no_hire' }
              ]
            },
            {
              stage: 'offer',
              next: [
                { stage: 'hired', outcome: 'accepted' },
                { stage: 'rejected', outcome: 'declined' }
              ]
            }
          ],
          stageConfig: [
            {
              stage: 'veda-review',
              scheduling: { type: 'IMMEDIATE' as const },
              action: {
                agentId: 'reviewAgent',
                outputs: ['qualified', 'not_qualified'],
                params: { scoreThreshold: 70 }
              },
              communicationChannel: 'EMAIL' as const
            },
            {
              stage: 'screening',
              scheduling: { type: 'BUSINESS_HOURS' as const, params: { timezone: 'America/New_York', startHour: 9, endHour: 17 } },
              action: {
                agentId: 'screeningAgent',
                outputs: ['pass', 'fail'],
                params: { duration: 30 }
              },
              communicationChannel: 'PLIVO' as const
            },
            {
              stage: 'assessment',
              scheduling: { type: 'IMMEDIATE' as const },
              action: {
                agentId: 'assessmentAgent',
                outputs: ['pass', 'fail'],
                params: { timeLimit: 120 }
              },
              communicationChannel: 'EMAIL' as const
            },
            {
              stage: 'interview',
              scheduling: { type: 'BUSINESS_HOURS' as const, params: { timezone: 'America/New_York', startHour: 9, endHour: 17 } },
              action: {
                agentId: 'interviewAgent',
                outputs: ['hire', 'no_hire'],
                params: { interviewType: 'technical' }
              },
              communicationChannel: 'CALENDAR' as const
            },
            {
              stage: 'offer',
              scheduling: { type: 'BUSINESS_HOURS' as const, params: { timezone: 'America/New_York', startHour: 9, endHour: 17 } },
              action: {
                agentId: 'notificationAgent',
                outputs: ['accepted', 'declined'],
                params: { offerValidDays: 7 }
              },
              communicationChannel: 'EMAIL' as const
            }
          ],
          createdAt: new Date(),
          updatedAt: new Date(),
        }

        loadWorkflow(sampleJobConfig)
      }
    }
  }, [selectedJobId, jobs, loadWorkflow])

  // Handle job selection
  const handleJobSelect = (jobId: string) => {
    setSelectedJobId(jobId)
    setSearchParams({ jobId })
  }

  // Handle creating new workflow
  const handleNewWorkflow = () => {
    if (selectedJobId) {
      createNewWorkflow(selectedJobId)
      toast({
        title: "New Workflow Created",
        description: "Start building your workflow by adding stages.",
      })
    }
  }

  // Handle saving workflow
  const handleSave = () => {
    const validation = validateWorkflow()

    if (!validation.isValid) {
      toast({
        title: "Validation Failed",
        description: validation.errors.join(', '),
        variant: "destructive",
      })
      return
    }

    const savedConfig = saveWorkflow()
    if (savedConfig) {
      toast({
        title: "Workflow Saved",
        description: "Your workflow has been saved successfully.",
      })
    }
  }

  const selectedJob = jobs?.find(j => j._id === selectedJobId)
  const validation = validateWorkflow()

  return (
    <div className="h-screen flex flex-col bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button variant="ghost" size="sm" onClick={() => window.history.back()}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Workflow Editor</h1>
              <p className="text-gray-600">
                {selectedJob ? `Editing workflow for: ${selectedJob.title}` : 'Select a job to edit its workflow'}
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            {/* Job Selection */}
            <Select value={selectedJobId} onValueChange={handleJobSelect}>
              <SelectTrigger className="w-64">
                <SelectValue placeholder="Select a job" />
              </SelectTrigger>
              <SelectContent>
                {jobs?.map((job) => (
                  <SelectItem key={job._id} value={job._id}>
                    <div>
                      <div className="font-medium">{job.title}</div>
                      <div className="text-xs text-gray-500">{job.department} • {job.location}</div>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {/* Validation Status */}
            <Badge variant={validation.isValid ? "default" : "destructive"}>
              {validation.isValid ? 'Valid' : `${validation.errors.length} Issues`}
            </Badge>

            {/* Action Buttons */}
            <Button variant="outline" onClick={handleNewWorkflow} disabled={!selectedJobId}>
              <Settings className="h-4 w-4 mr-2" />
              New Workflow
            </Button>

            <Button onClick={handleSave} disabled={!selectedJobId || nodes.length === 0}>
              <Save className="h-4 w-4 mr-2" />
              Save Workflow
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex">
        {selectedJobId ? (
          <div className="flex-1 relative">
            <WorkflowCanvas jobId={selectedJobId} />
          </div>
        ) : (
          <div className="flex-1 flex items-center justify-center">
            <Card className="w-96">
              <CardHeader>
                <CardTitle className="text-center">Get Started</CardTitle>
              </CardHeader>
              <CardContent className="text-center space-y-4">
                <p className="text-gray-600">
                  Select a job from the dropdown above to start creating or editing its recruitment workflow.
                </p>
                {jobsLoading ? (
                  <p className="text-sm text-gray-500">Loading jobs...</p>
                ) : (
                  <p className="text-sm text-gray-500">
                    {jobs?.length || 0} jobs available
                  </p>
                )}
              </CardContent>
            </Card>
          </div>
        )}
      </div>

      {/* Status Bar */}
      {selectedJobId && (
        <div className="bg-white border-t border-gray-200 px-6 py-2">
          <div className="flex items-center justify-between text-sm text-gray-600">
            <div className="flex items-center space-x-6">
              <span>Stages: {nodes.length}</span>
              <span>Connections: {edges.length}</span>
              <span>Configured: {nodes.filter(n => n.data.isConfigured).length}/{nodes.length}</span>
            </div>

            {!validation.isValid && (
              <div className="flex items-center space-x-2 text-red-600">
                <AlertTriangle className="h-4 w-4" />
                <span>{validation.errors.length} validation issue(s)</span>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Stage Configuration Panel */}
      <StageConfigPanel />
    </div>
  )
}
