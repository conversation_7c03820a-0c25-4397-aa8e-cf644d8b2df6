import { create } from 'zustand'
import {
  addEdge,
  applyNode<PERSON>hang<PERSON>,
  applyEdgeChanges,
  type Node,
  type Edge,
  type <PERSON>deChang<PERSON>,
  type EdgeChange,
  type Connection
} from 'reactflow'
import type { IJobConfig, IStatusConfig, IStageConfig, ICommunicationChannel, IStageScheduling, IStageAction, StageOutputsResponse, OutputMapping } from '@/types'

// Extended node data for workflow stages
export interface WorkflowStageData {
  stage: string
  label: string
  agentId?: string
  outputs?: string[]               // Static outputs from stage mapping
  predefinedOutputs?: string[]     // READ-ONLY: Outputs from agent definition
  outputMappings?: OutputMapping[] // USER-CONFIGURABLE: Output to stage mappings
  params?: Record<string, any>
  scheduling?: IStageScheduling
  communicationChannel?: ICommunicationChannel
  isConfigured?: boolean
}

// Extended node type for workflow
export interface WorkflowNode extends Node {
  data: WorkflowStageData
}

// Workflow store state
interface WorkflowState {
  // Canvas state
  nodes: WorkflowNode[]
  edges: Edge[]
  selectedNode: WorkflowNode | null

  // Configuration state
  currentJobConfig: IJobConfig | null
  isConfigPanelOpen: boolean

  // New state for output mapping
  stageOutputsCache: Map<string, StageOutputsResponse> // Cache stage outputs
  pendingMappingChanges: Map<string, OutputMapping[]> // Unsaved changes
  
  // Canvas actions
  setNodes: (nodes: WorkflowNode[]) => void
  setEdges: (edges: Edge[]) => void
  onNodesChange: (changes: NodeChange[]) => void
  onEdgesChange: (changes: EdgeChange[]) => void
  onConnect: (connection: Connection) => void
  
  // Node selection and configuration
  selectNode: (node: WorkflowNode | null) => void
  updateNodeData: (nodeId: string, data: Partial<WorkflowStageData>) => void
  
  // Configuration panel
  openConfigPanel: () => void
  closeConfigPanel: () => void
  
  // Workflow management
  loadWorkflow: (jobConfig: IJobConfig) => void
  saveWorkflow: () => IJobConfig | null
  createNewWorkflow: (jobId: string) => void

  // Enhanced actions for output mapping
  updateOutputMappings: (stage: string, mappings: OutputMapping[]) => void
  loadStageOutputs: (stage: string, outputs: StageOutputsResponse) => void
  generateEdgesFromMappings: () => void // Regenerate visual edges from mappings
  addStageNode: (stage: string, position: { x: number; y: number }) => void
  removeNode: (nodeId: string) => void

  // Computed properties
  selectedStageId: string | null

  // Utility functions
  getNodeById: (id: string) => WorkflowNode | undefined
  validateWorkflow: () => { isValid: boolean; errors: string[] }
}

// Static stage-agent-output mappings
const stageAgentMappings = {
  'veda-review': {
    agentId: 'reviewAgent',
    outputs: ['best', 'good', 'bad'],
    defaultParams: {
      reviewCriteria: 'Match candidate profile against job description keywords and basic qualifications.'
    }
  },
  'screening': {
    agentId: 'screeningAgent',
    outputs: ['pass', 'fail'],
    defaultParams: {
      screeningQuestions: [
        {
          id: 1,
          question: 'Are you comfortable with remote work?',
          options: ['Yes', 'No'],
          correctAnswer: 'Yes'
        }
      ]
    }
  },
  'assessment': {
    agentId: 'assessmentAgent',
    outputs: ['qualified', 'not-qualified'],
    defaultParams: {
      assessmentType: 'technical',
      duration: 60
    }
  },
  'interview': {
    agentId: 'interviewAgent',
    outputs: ['pass', 'fail'],
    defaultParams: {
      interviewType: 'technical',
      duration: 45
    }
  },
  'final-review': {
    agentId: 'reviewAgent',
    outputs: ['approved', 'rejected'],
    defaultParams: {
      finalCriteria: 'Overall candidate evaluation'
    }
  }
} as const

// Default stage configurations
const defaultStages = [
  { id: 'veda-review', label: 'Veda Review', color: '#8B5CF6' },
  { id: 'screening', label: 'Screening', color: '#F59E0B' },
  { id: 'assessment', label: 'Assessment', color: '#EF4444' },
  { id: 'interview', label: 'Interview', color: '#10B981' },
  { id: 'final-review', label: 'Final Review', color: '#3B82F6' },
  { id: 'offer', label: 'Offer', color: '#8B5CF6' },
  { id: 'rejected', label: 'Rejected', color: '#6B7280' },
  { id: 'hired', label: 'Hired', color: '#059669' },
]

// Create the workflow store
export const useWorkflowStore = create<WorkflowState>((set, get) => ({
  // Initial state
  nodes: [],
  edges: [],
  selectedNode: null,
  currentJobConfig: null,
  isConfigPanelOpen: false,
  stageOutputsCache: new Map(),
  pendingMappingChanges: new Map(),

  // Computed properties
  get selectedStageId() {
    return get().selectedNode?.id || null
  },

  // Canvas actions
  setNodes: (nodes) => set({ nodes }),
  setEdges: (edges) => set({ edges }),
  
  onNodesChange: (changes) => {
    set({
      nodes: applyNodeChanges(changes, get().nodes)
    })
  },
  
  onEdgesChange: (changes) => {
    set({
      edges: applyEdgeChanges(changes, get().edges)
    })
  },
  
  onConnect: (connection) => {
    const { edges } = get()
    const newEdge = {
      ...connection,
      id: `${connection.source}-${connection.target}`,
      type: 'smoothstep',
      animated: true,
      style: { stroke: '#6B7280', strokeWidth: 2 },
    }
    set({
      edges: addEdge(newEdge, edges)
    })
  },

  // Node selection and configuration
  selectNode: (node) => {
    set({ 
      selectedNode: node,
      isConfigPanelOpen: !!node
    })
  },
  
  updateNodeData: (nodeId, newData) => {
    const { nodes } = get()
    const updatedNodes = nodes.map(node => 
      node.id === nodeId 
        ? { 
            ...node, 
            data: { 
              ...node.data, 
              ...newData,
              isConfigured: true
            }
          }
        : node
    )
    set({ nodes: updatedNodes })
  },

  // Configuration panel
  openConfigPanel: () => set({ isConfigPanelOpen: true }),
  closeConfigPanel: () => set({ isConfigPanelOpen: false, selectedNode: null }),

  // Workflow management
  loadWorkflow: (jobConfig) => {
    const nodes: WorkflowNode[] = []
    const edges: Edge[] = []
    
    // Create nodes from stageConfig
    jobConfig.stageConfig.forEach((stageConfig, index) => {
      const stageInfo = defaultStages.find(s => s.id === stageConfig.stage) || 
                       { id: stageConfig.stage, label: stageConfig.stage, color: '#6B7280' }
      
      nodes.push({
        id: stageConfig.stage,
        type: 'workflowStage',
        position: { x: 200 + (index % 3) * 300, y: 100 + Math.floor(index / 3) * 200 },
        data: {
          stage: stageConfig.stage,
          label: stageInfo.label,
          agentId: stageConfig.action.agentId,
          outputs: stageConfig.action.outputs,
          params: stageConfig.action.params,
          scheduling: stageConfig.scheduling,
          communicationChannel: stageConfig.communicationChannel,
          isConfigured: true,
        },
        style: {
          backgroundColor: stageInfo.color,
          color: 'white',
          border: 'none',
          borderRadius: '8px',
        }
      })
    })
    
    // Create edges from flow configuration
    jobConfig.flow.forEach((flowConfig) => {
      flowConfig.next.forEach((nextConfig, index) => {
        edges.push({
          id: `${flowConfig.stage}-${nextConfig.stage}-${index}`,
          source: flowConfig.stage,
          target: nextConfig.stage,
          type: 'smoothstep',
          animated: true,
          label: nextConfig.outcome,
          style: { stroke: '#6B7280', strokeWidth: 2 },
          labelStyle: { fontSize: '12px', fontWeight: 'bold' },
        })
      })
    })
    
    set({ 
      currentJobConfig: jobConfig,
      nodes,
      edges,
      selectedNode: null,
      isConfigPanelOpen: false
    })
  },

  saveWorkflow: () => {
    const { nodes, edges, currentJobConfig } = get()
    
    if (!currentJobConfig) return null
    
    // Build stageConfig from nodes
    const stageConfig: IStageConfig[] = nodes.map(node => ({
      stage: node.data.stage,
      scheduling: node.data.scheduling,
      action: {
        agentId: node.data.agentId || 'defaultAgent',
        outputs: node.data.outputs || ['success', 'fail'],
        params: node.data.params || {},
      },
      communicationChannel: node.data.communicationChannel,
    }))
    
    // Build flow from edges
    const flowMap = new Map<string, { stage: string; outcome: string }[]>()
    
    edges.forEach(edge => {
      if (!flowMap.has(edge.source)) {
        flowMap.set(edge.source, [])
      }
      flowMap.get(edge.source)!.push({
        stage: edge.target,
        outcome: edge.label || 'default'
      })
    })
    
    const flow: IStatusConfig[] = Array.from(flowMap.entries()).map(([stage, next]) => ({
      stage,
      next
    }))
    
    return {
      ...currentJobConfig,
      flow,
      stageConfig,
      updatedAt: new Date(),
    }
  },

  createNewWorkflow: (jobId) => {
    const newJobConfig: IJobConfig = {
      _id: `config_${Date.now()}`,
      jobId,
      flow: [],
      stageConfig: [],
      createdAt: new Date(),
      updatedAt: new Date(),
    }

    set({
      currentJobConfig: newJobConfig,
      nodes: [],
      edges: [],
      selectedNode: null,
      isConfigPanelOpen: false,
      stageOutputsCache: new Map(),
      pendingMappingChanges: new Map(),
    })
  },

  // Enhanced methods for output mapping
  updateOutputMappings: (stage: string, mappings: OutputMapping[]) => {
    const { pendingMappingChanges } = get()
    const newChanges = new Map(pendingMappingChanges)
    newChanges.set(stage, mappings)

    set({ pendingMappingChanges: newChanges })

    // Regenerate visual edges
    get().generateEdgesFromMappings()
  },

  loadStageOutputs: (stage: string, outputs: StageOutputsResponse) => {
    const { stageOutputsCache } = get()
    const newCache = new Map(stageOutputsCache)
    newCache.set(stage, outputs)

    set({ stageOutputsCache: newCache })
  },

  generateEdgesFromMappings: () => {
    const { nodes, stageOutputsCache, pendingMappingChanges } = get()
    const newEdges: Edge[] = []

    // Utility function for consistent output coloring
    const getOutputColor = (output: string): string => {
      const colors: Record<string, string> = {
        'best': '#22c55e',    // green
        'good': '#3b82f6',    // blue
        'bad': '#ef4444',     // red
        'pass': '#22c55e',    // green
        'fail': '#ef4444',    // red
        'qualified': '#22c55e',
        'not-qualified': '#ef4444',
      }
      return colors[output] || '#6b7280' // gray fallback
    }

    nodes.forEach(node => {
      if (node.type === 'stage') {
        const stageData = node.data as WorkflowStageData
        const stage = stageData.stage
        const mappings = pendingMappingChanges.get(stage) ||
                        stageOutputsCache.get(stage)?.currentMappings || []

        mappings.forEach((mapping) => {
          const targetNode = nodes.find(n =>
            n.type === 'stage' && (n.data as WorkflowStageData).stage === mapping.targetStage
          )

          if (targetNode) {
            newEdges.push({
              id: `${node.id}-${mapping.output}-${targetNode.id}`,
              source: node.id,
              target: targetNode.id,
              sourceHandle: mapping.output,
              label: mapping.output,
              data: {
                output: mapping.output,
                outcome: mapping.output, // Legacy compatibility
                isConfigurable: true
              },
              style: {
                stroke: getOutputColor(mapping.output),
                strokeWidth: 2,
              }
            })
          }
        })
      }
    })

    set({ edges: newEdges })
  },

  addStageNode: (stage, position) => {
    const { nodes } = get()
    const stageInfo = defaultStages.find(s => s.id === stage) ||
                     { id: stage, label: stage, color: '#6B7280' }
    const mapping = stageAgentMappings[stage as keyof typeof stageAgentMappings]

    const newNode: WorkflowNode = {
      id: `${stage}_${Date.now()}`,
      type: 'workflowStage',
      position,
      data: {
        stage,
        label: stageInfo.label,
        agentId: mapping?.agentId,
        outputs: mapping?.outputs,
        params: mapping?.defaultParams,
        isConfigured: false,
      },
      style: {
        backgroundColor: stageInfo.color,
        color: 'white',
        border: 'none',
        borderRadius: '8px',
      }
    }

    set({ nodes: [...nodes, newNode] })
  },

  removeNode: (nodeId) => {
    const { nodes, edges, selectedNode } = get()

    // Remove the node
    const updatedNodes = nodes.filter(node => node.id !== nodeId)

    // Remove all edges connected to this node
    const updatedEdges = edges.filter(edge =>
      edge.source !== nodeId && edge.target !== nodeId
    )

    // Clear selection if the removed node was selected
    const updatedSelectedNode = selectedNode?.id === nodeId ? null : selectedNode

    set({
      nodes: updatedNodes,
      edges: updatedEdges,
      selectedNode: updatedSelectedNode,
      isConfigPanelOpen: updatedSelectedNode ? get().isConfigPanelOpen : false
    })
  },

  // Utility functions
  getNodeById: (id) => {
    return get().nodes.find(node => node.id === id)
  },

  validateWorkflow: () => {
    const { nodes, edges } = get()
    const errors: string[] = []
    
    // Check if workflow has nodes
    if (nodes.length === 0) {
      errors.push('Workflow must have at least one stage')
    }
    
    // Check if all nodes are configured
    const unconfiguredNodes = nodes.filter(node => !node.data.isConfigured)
    if (unconfiguredNodes.length > 0) {
      errors.push(`${unconfiguredNodes.length} stage(s) need configuration`)
    }
    
    // Check for disconnected nodes (except start/end nodes)
    nodes.forEach(node => {
      const hasIncoming = edges.some(edge => edge.target === node.id)
      const hasOutgoing = edges.some(edge => edge.source === node.id)
      
      if (!hasIncoming && !hasOutgoing && node.data.stage !== 'start') {
        errors.push(`Stage "${node.data.label}" is not connected`)
      }
    })
    
    return {
      isValid: errors.length === 0,
      errors
    }
  },

  // Generate JobConfig object from current workflow
  generateJobConfig: (jobId: string): IJobConfig => {
    const { nodes, edges } = get()

    // Generate flow array based on edges
    const flow: IStatusConfig[] = []
    const stageConfig: IStageConfig[] = []

    nodes.forEach(node => {
      const stage = node.data.stage
      const mapping = stageAgentMappings[stage as keyof typeof stageAgentMappings]

      if (!mapping) return

      // Find outgoing edges for this node
      const outgoingEdges = edges.filter(edge => edge.source === node.id)

      // Generate next stages based on outputs and connections
      const next: { stage: string; outcome: string }[] = []

      if (outgoingEdges.length > 0) {
        // If there are connections, map outputs to connected stages
        mapping.outputs.forEach((output, index) => {
          const targetEdge = outgoingEdges[index] || outgoingEdges[0] // Use first connection if not enough
          const targetNode = nodes.find(n => n.id === targetEdge.target)

          if (targetNode) {
            next.push({
              stage: targetNode.data.stage,
              outcome: output
            })
          }
        })
      } else {
        // If no connections, map to terminal states
        mapping.outputs.forEach(output => {
          if (['best', 'good', 'pass', 'approved'].includes(output)) {
            next.push({ stage: 'stop', outcome: output })
          } else {
            next.push({ stage: 'rejected', outcome: output })
          }
        })
      }

      // Add to flow
      flow.push({
        stage,
        next
      })

      // Add to stageConfig
      stageConfig.push({
        stage,
        action: {
          agentId: mapping.agentId,
          outputs: mapping.outputs,
          params: node.data.params || mapping.defaultParams
        },
        communicationChannel: node.data.communicationChannel || 'EMAIL',
        scheduling: node.data.scheduling || { type: 'IMMEDIATE' }
      })
    })

    return {
      _id: `config_${jobId}_${Date.now()}`,
      jobId,
      flow,
      stageConfig
    }
  },
}))

// Export default stages and mappings for use in components
export { defaultStages, stageAgentMappings }
