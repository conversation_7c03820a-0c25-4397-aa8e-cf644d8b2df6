import { useCallback, useMemo } from 'react'
import <PERSON>act<PERSON>low, {
  Background,
  Controls,
  MiniMap,
  ReactFlowProvider,
  Panel,
  type Connection,
  type Edge,
} from 'reactflow'
import { CheckCircle, AlertTriangle } from 'lucide-react'
import 'reactflow/dist/style.css'

import { WorkflowStageNode } from './WorkflowStageNode'
import { StageConfigPanel } from './StageConfigPanel'
import { WorkflowToolbar } from './WorkflowToolbar'
import { useWorkflowStore } from '@/stores/workflowStore'

// Define custom node types
const nodeTypes = {
  workflowStage: WorkflowStageNode,
}

// Custom edge styles
const defaultEdgeOptions = {
  animated: true,
  style: {
    strokeWidth: 2,
    stroke: '#6B7280',
  },
}

interface WorkflowCanvasProps {
  jobId?: string
  readonly?: boolean
}

function WorkflowCanvasInner({ jobId, readonly = false }: WorkflowCanvasProps) {
  const {
    nodes,
    edges,
    onNodesChange,
    onEdgesChange,
    onConnect,
    selectNode,
    isConfigPanelOpen,
    validateWorkflow,
  } = useWorkflowStore()

  // Handle node click for selection
  const handleNodeClick = useCallback((event: React.MouseEvent, node: any) => {
    if (!readonly) {
      selectNode(node)
    }
  }, [selectNode, readonly])

  // Handle pane click to deselect
  const handlePaneClick = useCallback(() => {
    if (!readonly) {
      selectNode(null)
    }
  }, [selectNode, readonly])

  // Handle connection creation
  const handleConnect = useCallback((params: Connection | Edge) => {
    if (!readonly) {
      onConnect(params)
    }
  }, [onConnect, readonly])

  // Memoize the ReactFlow props to prevent unnecessary re-renders
  const reactFlowProps = useMemo(() => ({
    nodes,
    edges,
    onNodesChange: readonly ? undefined : onNodesChange,
    onEdgesChange: readonly ? undefined : onEdgesChange,
    onConnect: handleConnect,
    onNodeClick: handleNodeClick,
    onPaneClick: handlePaneClick,
    nodeTypes,
    defaultEdgeOptions,
    fitView: true,
    attributionPosition: 'bottom-left' as const,
    proOptions: { hideAttribution: true },
  }), [
    nodes,
    edges,
    onNodesChange,
    onEdgesChange,
    handleConnect,
    handleNodeClick,
    handlePaneClick,
    readonly
  ])

  return (
    <div className="w-full h-full relative">
      <ReactFlow {...reactFlowProps}>
        {/* Background with dots pattern */}
        <Background 
          color="#e5e7eb" 
          gap={20} 
          size={1}
          variant="dots" 
        />
        
        {/* Controls for zoom, fit view, etc. */}
        <Controls 
          position="bottom-right"
          showInteractive={!readonly}
        />
        
        {/* Mini map for navigation */}
        <MiniMap 
          position="bottom-left"
          nodeColor={(node) => {
            switch (node.data?.stage) {
              case 'veda-review': return '#8B5CF6'
              case 'screening': return '#F59E0B'
              case 'assessment': return '#EF4444'
              case 'interview': return '#10B981'
              case 'final-review': return '#3B82F6'
              case 'offer': return '#8B5CF6'
              case 'rejected': return '#6B7280'
              case 'hired': return '#059669'
              default: return '#6B7280'
            }
          }}
          maskColor="rgba(0, 0, 0, 0.1)"
        />

        {/* Enhanced Workflow Info Panel */}
        <Panel position="top-right">
          <div className="bg-white rounded-lg shadow-lg p-4 max-w-sm border border-gray-200">
            <div className="flex items-center justify-between mb-3">
              <h3 className="font-semibold text-gray-900">Workflow Overview</h3>
              <div className="flex items-center space-x-1">
                {validateWorkflow().isValid ? (
                  <div className="flex items-center text-green-600">
                    <CheckCircle className="h-4 w-4 mr-1" />
                    <span className="text-xs font-medium">Valid</span>
                  </div>
                ) : (
                  <div className="flex items-center text-red-600">
                    <AlertTriangle className="h-4 w-4 mr-1" />
                    <span className="text-xs font-medium">Issues</span>
                  </div>
                )}
              </div>
            </div>

            <div className="space-y-3">
              {/* Stats Grid */}
              <div className="grid grid-cols-2 gap-3">
                <div className="bg-blue-50 p-3 rounded-lg">
                  <div className="text-lg font-bold text-blue-600">{nodes.length}</div>
                  <div className="text-xs text-blue-600 font-medium">Stages</div>
                </div>
                <div className="bg-green-50 p-3 rounded-lg">
                  <div className="text-lg font-bold text-green-600">{edges.length}</div>
                  <div className="text-xs text-green-600 font-medium">Connections</div>
                </div>
                <div className="bg-purple-50 p-3 rounded-lg">
                  <div className="text-lg font-bold text-purple-600">
                    {nodes.filter(n => n.data.isConfigured).length}
                  </div>
                  <div className="text-xs text-purple-600 font-medium">Configured</div>
                </div>
                <div className="bg-orange-50 p-3 rounded-lg">
                  <div className="text-lg font-bold text-orange-600">
                    {validateWorkflow().errors.length}
                  </div>
                  <div className="text-xs text-orange-600 font-medium">Issues</div>
                </div>
              </div>

              {/* Progress Bar */}
              {nodes.length > 0 && (
                <div className="space-y-2">
                  <div className="flex justify-between text-xs text-gray-600">
                    <span>Configuration Progress</span>
                    <span>{Math.round((nodes.filter(n => n.data.isConfigured).length / nodes.length) * 100)}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{
                        width: `${(nodes.filter(n => n.data.isConfigured).length / nodes.length) * 100}%`
                      }}
                    />
                  </div>
                </div>
              )}

              {/* Validation Errors */}
              {!validateWorkflow().isValid && (
                <div className="space-y-1">
                  <div className="text-xs font-medium text-red-700">Issues to Fix:</div>
                  <div className="space-y-1 max-h-20 overflow-y-auto">
                    {validateWorkflow().errors.slice(0, 3).map((error, index) => (
                      <div key={`error-${index}`} className="text-xs text-red-600 bg-red-50 p-2 rounded border-l-2 border-red-200">
                        {error}
                      </div>
                    ))}
                    {validateWorkflow().errors.length > 3 && (
                      <div className="text-xs text-gray-500 text-center">
                        +{validateWorkflow().errors.length - 3} more issues
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        </Panel>
      </ReactFlow>

      {/* Configuration Panel */}
      {!readonly && <StageConfigPanel />}

      {/* Floating Toolbar - positioned inside the canvas */}
      {!readonly && <WorkflowToolbar jobId={jobId} />}
    </div>
  )
}

export function WorkflowCanvas(props: WorkflowCanvasProps) {
  return (
    <ReactFlowProvider>
      <WorkflowCanvasInner {...props} />
    </ReactFlowProvider>
  )
}
