import React from 'react'

// Base MongoDB ObjectId type
export type ObjectId = string;

// Organization Model
export interface IOrganization {
  _id: ObjectId;
  name: string; // required
  address?: string;
  domain: string; // required
  createdAt: Date;
  updatedAt: Date;
}

// Recruiter Model
export interface IRecruiter {
  _id: ObjectId;
  name: string; // required
  email: string; // required, unique
  organization: ObjectId; // required, ref: Organization
  password: string; // required
  createdAt: Date;
  updatedAt: Date;
}

// Job Model Types
export const JobType = {
  FULL_TIME: "full_time",
  PART_TIME: "part_time",
  CONTRACT: "contract",
  INTERNSHIP: "internship"
} as const

export type JobType = typeof JobType[keyof typeof JobType]

export const WorkLocation = {
  REMOTE: "remote",
  ONSITE: "onsite",
  HYBRID: "hybrid"
} as const

export type WorkLocation = typeof WorkLocation[keyof typeof WorkLocation]

export const ExperienceLevel = {
  ENTRY: "entry",
  MID: "mid",
  SENIOR: "senior",
  LEAD: "lead",
  EXECUTIVE: "executive"
} as const

export type ExperienceLevel = typeof ExperienceLevel[keyof typeof ExperienceLevel]

// Stage Status for Candidates
export const StageStatus = {
  REGISTERED: "registered",
  PENDING_SCHEDULE: "pending_schedule",
  QUEUED: "queued",
  IN_PROGRESS: "in_progress",
  AWAITING_RESULT: "awaiting_result",
  COMPLETED_SUCCESS: "completed_success",
  COMPLETED_FAIL: "completed_fail",
  WORKFLOW_TERMINATED: "workflow_terminated",
  ERROR: "error"
} as const

export type StageStatus = typeof StageStatus[keyof typeof StageStatus]

export const JobStatus = {
  DRAFT: "draft",
  ACTIVE: "active",
  PAUSED: "paused",
  CLOSED: "closed",
  CANCELLED: "cancelled"
} as const

export type JobStatus = typeof JobStatus[keyof typeof JobStatus]

// Workflow Status for Jobs
export const WorkflowStatus = {
  NOT_CONFIGURED: "not_configured",
  CONFIGURED: "configured",
  RUNNING: "running",
  PAUSED: "paused",
  STOPPED: "stopped",
  ERROR: "error"
} as const

export type WorkflowStatus = typeof WorkflowStatus[keyof typeof WorkflowStatus]

// Job Model Interfaces
export interface RequiredSkill {
  name: string;
  level: "required" | "preferred" | "nice_to_have";
  yearsRequired?: number;
}

export interface SalaryRange {
  min: number; // min: 0
  max: number; // min: 0
  currency: string; // default: "USD", max 3 chars, uppercase
  period: "hourly" | "monthly" | "yearly"; // default: "yearly"
}

export interface IJob {
  _id: ObjectId;
  title: string; // required, max 200 chars
  description: string; // required, max 5000 chars
  department: string; // required, max 100 chars
  location: string; // required, max 200 chars
  jobType: JobType; // required
  workLocation: WorkLocation; // required
  experienceLevel: ExperienceLevel; // required
  requiredSkills: RequiredSkill[];
  qualifications: string[]; // max 500 chars each
  responsibilities: string[]; // max 500 chars each
  salaryRange?: SalaryRange;
  status: JobStatus; // required, default: "draft"
  openings: number; // required, min: 1, default: 1
  applicationDeadline?: Date;
  startDate?: Date;
  postedDate: Date; // required, default: now
  // Workflow tracking fields (optional until backend is updated)
  workflowStatus?: WorkflowStatus; // optional, default: "not_configured"
  workflowId?: string; // ID of the running workflow instance
  workflowStartedAt?: Date; // When workflow was started
  workflowStoppedAt?: Date; // When workflow was stopped
  createdAt: Date;
  updatedAt: Date;
  createdBy: ObjectId; // Reference to Recruiter
  organization: ObjectId; // Reference to Organization
}

// Candidate Model Types

// Candidate Model Interfaces
export interface ContactInfo {
  email: string; // required, valid email
  phone?: string;
  linkedin?: string;
  github?: string;
  address?: string;
}

export interface ICandidate {
  _id: ObjectId;
  sourceUid?: string; // Optional as per API docs
  name: string; // required, max 100 chars
  email: string; // required, valid email format
  phone?: string; // Optional, valid phone format
  resumeLink: string; // required
  jobId: ObjectId; // required, ref: Job
  stage: string; // required, default: "registered"
  expectedSalary?: number; // min: 0
  contactInfo: ContactInfo;
  status: StageStatus; // required, default: "registered"
  source: string; // required
  createdAt: Date;
  updatedAt: Date;
  createdBy?: string;
}

// JobConfig Model (Workflow Editor)
export const ICommunicationChannel = {
  EMAIL: "EMAIL",
  PLIVO: "PLIVO",
  WHATSAPP: "WHATSAPP",
  SLACK: "SLACK",
  CALENDAR: "CALENDAR"
} as const

export type ICommunicationChannel = typeof ICommunicationChannel[keyof typeof ICommunicationChannel]

export interface INextStatusConfig {
  stage: string; // required
  outcome: string; // required
}

export interface IStatusConfig {
  stage: string; // required
  next: INextStatusConfig[]; // required
}

export interface IStageAction {
  agentId: string;
  outputs?: string[];
  params: Record<string, unknown>;
}

export interface IStageScheduling {
  type: "IMMEDIATE" | "BUSINESS_HOURS";
  params?: {
    timezone?: string;
    startHour?: number;
    endHour?: number;
  };
}

export interface IStageConfig {
  stage: string;
  scheduling?: IStageScheduling;
  action: IStageAction;
  communicationChannel?: ICommunicationChannel;
}

export interface IJobConfig {
  _id: ObjectId;
  jobId: ObjectId; // required, ref: Job
  flow: IStatusConfig[]; // required
  stageConfig: IStageConfig[]; // required
  createdAt: Date;
  updatedAt: Date;
}

// Explicit re-export to ensure proper module resolution
export type { IJobConfig as JobConfig }

// Workflow Editor Types
export interface WorkflowNode {
  id: string;
  type: 'stage' | 'condition' | 'start' | 'end';
  position: { x: number; y: number };
  data: StageNodeData | ConditionNodeData;
  style?: React.CSSProperties;
}

export interface WorkflowEdge {
  id: string;
  source: string;
  target: string;
  sourceHandle?: string;
  targetHandle?: string;
  label?: string;       // Shows the output name (e.g., "best")
  data?: {
    output: string;     // The predefined output that triggers this edge
    outcome: string;    // Legacy field for backward compatibility
    isConfigurable: boolean; // Whether user can change this mapping
  };
  style?: {
    stroke: string;     // Color-coded by output type
  };
}

export interface StageNodeData {
  stage: string;
  agentId: string;
  predefinedOutputs: string[];     // READ-ONLY: Outputs from agent definition
  outputMappings: OutputMapping[]; // USER-CONFIGURABLE: Output to stage mappings
  params: Record<string, any>;
  scheduling?: IStageScheduling;
  communicationChannel?: ICommunicationChannel;
}

// New Output Mapping Interface
export interface OutputMapping {
  output: string;      // Predefined output from agent (e.g., "best", "good", "bad")
  targetStage: string; // User-selected target stage
}

export interface ConditionNodeData {
  condition: string;
  logic: Record<string, any>;
}

export interface WorkflowCanvas {
  nodes: WorkflowNode[];
  edges: WorkflowEdge[];
  viewport: { x: number; y: number; zoom: number };
  selectedNodes: string[];
  selectedEdges: string[];
}

export interface SavedWorkflow {
  id: string;
  name: string;
  description?: string;
  jobId: string;
  nodes: WorkflowNode[];
  edges: WorkflowEdge[];
  flow: IStatusConfig[];
  stageConfig: IStageConfig[];
  createdAt: Date;
  updatedAt: Date;
  version: number;
}

// API Response Types for Stage Outputs
export interface StageOutputsResponse {
  stage: string;
  predefinedOutputs: string[];
  availableTargetStages: string[];
  currentMappings: OutputMapping[];
  agentId: string;
}

// Assessment Configuration Types
export enum AssessmentType {
  AI_INTERVIEW = "ai-interview",
  MANUAL = "manual",
  HYBRID = "hybrid"
}

export enum DifficultyLevel {
  EASY = "easy",
  MEDIUM = "medium",
  HARD = "hard"
}

export enum SequenceType {
  AI_FIRST = "ai-first",
  MANUAL_FIRST = "manual-first",
  PARALLEL = "parallel"
}

export interface IAssessmentQuestion {
  topic: string;
  questionText: string;
  followUpQuestions?: string[];
  difficulty?: DifficultyLevel;
}

export interface IAIInterviewConfig {
  questions: IAssessmentQuestion[];
  isProctoringRequired: boolean;
  interviewDuration?: number;
  difficultyLevel?: DifficultyLevel;
  topics?: string[];
  maxRetries?: number;
  recordingEnabled?: boolean;
}

export interface IManualAssessmentConfig {
  assessmentPlatform: string;
  assessmentLink: string;
  timeLimit?: number;
  passingScore?: number;
  instructions?: string;
  maxAttempts?: number;
  autoGrading?: boolean;
}

export interface ICombinedPassingCriteria {
  aiWeight: number;
  manualWeight: number;
  minimumCombinedScore: number;
}

export interface IHybridAssessmentConfig {
  aiInterviewConfig: IAIInterviewConfig;
  manualAssessmentConfig: IManualAssessmentConfig;
  sequence: SequenceType;
  combinedPassingCriteria?: ICombinedPassingCriteria;
}

export interface IAssessmentConfig {
  _id: ObjectId;
  jobId: ObjectId;
  stage: string;
  assessmentType: AssessmentType;
  aiInterviewConfig?: IAIInterviewConfig;
  manualAssessmentConfig?: IManualAssessmentConfig;
  hybridAssessmentConfig?: IHybridAssessmentConfig;
  isActive: boolean;
  createdBy: ObjectId;
  organization: ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

export interface AssessmentTypesResponse {
  assessmentTypes: {
    [key: string]: {
      name: string;
      description: string;
      requiredFields: string[];
      optionalFields: string[];
      fieldDefinitions: Record<string, string>;
    };
  };
  difficultyLevels: DifficultyLevel[];
  sequenceOptions: SequenceType[];
}

export interface CreateAssessmentRequest {
  jobId: string;
  stage: string;
  assessmentType: AssessmentType;
  aiInterviewConfig?: IAIInterviewConfig;
  manualAssessmentConfig?: IManualAssessmentConfig;
  hybridAssessmentConfig?: IHybridAssessmentConfig;
}

export interface UpdateAssessmentRequest extends Partial<CreateAssessmentRequest> {
  isActive?: boolean;
}

// API Response Types
export interface ApiResponse<T> {
  data: T;
  message?: string;
  success: boolean;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// CSV Upload Types
export interface CSVCandidateData {
  name: string;
  email: string;
  phone?: string;
  resumeLink: string;
  jobId: string;
  stage?: string;
  expectedSalary?: number;
  source: string;
  'contactInfo.linkedin'?: string;
  'contactInfo.github'?: string;
  'contactInfo.address'?: string;
}

export interface CSVUploadResponse {
  message: string;
  summary: {
    totalRows: number;
    successfulInserts: number;
    failedRows: number;
  };
  insertedCandidates: ICandidate[];
  errors: CSVUploadError[];
}

export interface CSVUploadError {
  row: number;
  data: Partial<CSVCandidateData>;
  errors: string[];
}

// Common UI Types
export interface TableColumn {
  key: string;
  label: string;
  sortable?: boolean;
  render?: (value: any, row: any) => React.ReactNode;
}

export interface FormProps<T> {
  mode: 'create' | 'edit';
  initialData?: Partial<T>;
  onSubmit: (data: T) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
}
