import { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { Plus, Edit, Eye, Trash2, Play, Settings, Briefcase, Calendar, CheckCircle, AlertTriangle } from 'lucide-react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { DataTable } from '@/components/tables/DataTable'
import { useJobs } from '@/hooks/useJobs'
import { formatDate } from '@/lib/utils'

// Mock workflow data (in a real app, this would come from an API)
const mockWorkflows = [
  {
    _id: '1',
    jobId: '1',
    name: 'Senior Frontend Developer Workflow',
    status: 'active',
    stagesCount: 5,
    connectionsCount: 8,
    configuredStages: 5,
    lastModified: new Date('2024-01-20T10:30:00Z'),
    createdAt: new Date('2024-01-15T09:00:00Z'),
    isValid: true,
  },
  {
    _id: '2',
    jobId: '2',
    name: 'Backend Developer Workflow',
    status: 'draft',
    stagesCount: 4,
    connectionsCount: 6,
    configuredStages: 3,
    lastModified: new Date('2024-01-19T14:20:00Z'),
    createdAt: new Date('2024-01-18T11:15:00Z'),
    isValid: false,
  },
  {
    _id: '3',
    jobId: '3',
    name: 'Product Manager Workflow',
    status: 'active',
    stagesCount: 6,
    connectionsCount: 10,
    configuredStages: 6,
    lastModified: new Date('2024-01-18T16:45:00Z'),
    createdAt: new Date('2024-01-16T13:30:00Z'),
    isValid: true,
  },
]

export function WorkflowList() {
  const navigate = useNavigate()
  const { data: jobs } = useJobs()
  const [workflows] = useState(mockWorkflows)

  const getJobTitle = (jobId: string) => {
    if (!jobs || !Array.isArray(jobs)) return 'Unknown Job'
    const job = jobs.find(j => j._id === jobId)
    return job?.title || 'Unknown Job'
  }

  const getJobDetails = (jobId: string) => {
    if (!jobs || !Array.isArray(jobs)) return 'N/A'
    const job = jobs.find(j => j._id === jobId)
    return job ? `${job.department} • ${job.location}` : 'N/A'
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800'
      case 'draft':
        return 'bg-yellow-100 text-yellow-800'
      case 'inactive':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const handleEdit = (workflowId: string) => {
    const workflow = workflows.find(w => w._id === workflowId)
    if (workflow) {
      navigate(`/workflows/editor?jobId=${workflow.jobId}`)
    }
  }

  const handleView = (workflowId: string) => {
    const workflow = workflows.find(w => w._id === workflowId)
    if (workflow) {
      navigate(`/workflows/editor?jobId=${workflow.jobId}&readonly=true`)
    }
  }

  const handleDelete = (workflowId: string) => {
    // In a real app, this would call an API to delete the workflow
    console.log('Delete workflow:', workflowId)
  }

  const handleCreateNew = () => {
    navigate('/workflows/editor')
  }

  const columns = [
    {
      key: 'name',
      label: 'Workflow',
      sortable: true,
      render: (value: string, row: any) => (
        <div className="min-w-[250px]">
          <div className="flex items-center space-x-3">
            <div className="flex-shrink-0">
              <div className="h-10 w-10 rounded-lg bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center">
                <Settings className="h-5 w-5 text-white" />
              </div>
            </div>
            <div className="min-w-0 flex-1">
              <div className="text-sm font-semibold text-gray-900 truncate">{value}</div>
              <div className="text-xs text-gray-500 flex items-center space-x-1 mt-1">
                <Briefcase className="h-3 w-3 flex-shrink-0" />
                <span className="truncate">{getJobTitle(row.jobId)}</span>
              </div>
              <div className="text-xs text-gray-500 mt-0.5">
                {getJobDetails(row.jobId)}
              </div>
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'status',
      label: 'Status',
      sortable: true,
      render: (value: string, row: any) => (
        <div className="min-w-[120px]">
          <div className="flex items-center space-x-2">
            <Badge className={`${getStatusColor(value)} border-0 text-xs px-2 py-1`}>
              {value.toUpperCase()}
            </Badge>
            {row.isValid ? (
              <CheckCircle className="h-4 w-4 text-green-500" />
            ) : (
              <AlertTriangle className="h-4 w-4 text-yellow-500" />
            )}
          </div>
        </div>
      )
    },
    {
      key: 'stagesCount',
      label: 'Stages',
      sortable: true,
      render: (value: number, row: any) => (
        <div className="min-w-[100px]">
          <div className="text-sm font-medium text-gray-900">{value}</div>
          <div className="text-xs text-gray-500">
            {row.configuredStages}/{value} configured
          </div>
        </div>
      )
    },
    {
      key: 'connectionsCount',
      label: 'Connections',
      sortable: true,
      render: (value: number) => (
        <div className="min-w-[100px]">
          <span className="text-sm font-medium text-gray-900">{value}</span>
        </div>
      )
    },
    {
      key: 'lastModified',
      label: 'Last Modified',
      sortable: true,
      render: (value: Date) => (
        <div className="min-w-[120px]">
          <div className="flex items-center space-x-1">
            <Calendar className="h-4 w-4 text-gray-400 flex-shrink-0" />
            <span className="text-sm text-gray-600">{formatDate(value)}</span>
          </div>
        </div>
      )
    },
  ]

  // Calculate stats
  const totalWorkflows = workflows.length
  const activeWorkflows = workflows.filter(w => w.status === 'active').length
  const draftWorkflows = workflows.filter(w => w.status === 'draft').length
  const validWorkflows = workflows.filter(w => w.isValid).length

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Workflow Management</h1>
          <p className="text-gray-600">Create and manage recruitment workflows for your jobs</p>
        </div>
        <Button onClick={handleCreateNew}>
          <Plus className="h-4 w-4 mr-2" />
          Create Workflow
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Workflows</CardTitle>
            <Settings className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalWorkflows}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active</CardTitle>
            <Play className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{activeWorkflows}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Drafts</CardTitle>
            <Edit className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{draftWorkflows}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Valid</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{validWorkflows}</div>
          </CardContent>
        </Card>
      </div>

      {/* Workflows Table */}
      <div className="bg-white rounded-lg shadow-sm">
        <DataTable
          data={workflows}
          columns={columns}
          loading={false}
          onEdit={handleEdit}
          onView={handleView}
          onDelete={handleDelete}
        />
      </div>
    </div>
  )
}
