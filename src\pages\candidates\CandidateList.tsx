import { useState } from 'react'
import { Plus, User, Mail, Phone, Briefcase, DollarSign, Clock, Upload, Download } from 'lucide-react'
// import { Checkbox } from '@/components/ui/checkbox'
import { Button } from '@/components/ui/button'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { DataTable } from '@/components/tables/DataTable'
import { CandidateForm } from '@/components/forms/CandidateForm'
import { CandidateDetail } from './CandidateDetail'
import { CandidateSearchFilter } from '@/components/search/CandidateSearchFilter'
import { CandidateStageManager } from '@/components/candidates/CandidateStageManager'
import { CSVUpload } from '@/components/candidates/CSVUpload'
// import { BulkStageManager } from '@/components/candidates/BulkStageManager'
import { useCandidates, useCreateCandidate, useUpdateCandidate, useDeleteCandidate, useDownloadCSVTemplate, type ICandidate, type CandidateFormData } from '@/hooks/useCandidates'
import { useJobs } from '@/hooks/useJobs'
import { useToast } from '@/hooks/use-toast'
import { formatDate } from '@/lib/utils'
import type { CSVUploadResponse } from '@/types'

export function CandidateList() {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isCSVUploadOpen, setIsCSVUploadOpen] = useState(false)
  const [editingCandidate, setEditingCandidate] = useState<ICandidate | null>(null)
  const [viewingCandidate, setViewingCandidate] = useState<ICandidate | null>(null)
  const [filteredCandidates, setFilteredCandidates] = useState<ICandidate[] | null>(null)
  const [selectedCandidates, setSelectedCandidates] = useState<ICandidate[]>([])

  const { data: candidates, isLoading } = useCandidates()
  const { data: jobs } = useJobs()
  const createMutation = useCreateCandidate()
  const updateMutation = useUpdateCandidate()
  const deleteMutation = useDeleteCandidate()
  const downloadTemplateMutation = useDownloadCSVTemplate()
  const { toast } = useToast()

  // Use filtered candidates if available, otherwise use all candidates
  const displayCandidates = filteredCandidates || candidates || []

  const handleCreate = async (data: CandidateFormData) => {
    await createMutation.mutateAsync(data)
    setIsCreateDialogOpen(false)
  }

  const handleUpdate = async (data: CandidateFormData) => {
    if (editingCandidate) {
      await updateMutation.mutateAsync({ id: editingCandidate._id, data })
      setEditingCandidate(null)
    }
  }

  const handleDelete = async (id: string) => {
    if (confirm('Are you sure you want to delete this candidate?')) {
      await deleteMutation.mutateAsync(id)
    }
  }

  const handleDownloadTemplate = async () => {
    try {
      await downloadTemplateMutation.mutateAsync()
      toast({
        title: 'Template downloaded',
        description: 'CSV template has been downloaded to your computer.',
      })
    } catch (error) {
      toast({
        title: 'Download failed',
        description: 'Failed to download CSV template. Please try again.',
        variant: 'destructive',
      })
    }
  }

  const handleCSVUploadSuccess = (result: CSVUploadResponse) => {
    toast({
      title: 'CSV Upload Complete',
      description: `Successfully imported ${result.summary.successfulInserts} out of ${result.summary.totalRows} candidates.`,
    })
    setIsCSVUploadOpen(false)
  }

  const handleSelectCandidate = (candidate: ICandidate, checked: boolean) => {
    if (checked) {
      setSelectedCandidates(prev => [...prev, candidate])
    } else {
      setSelectedCandidates(prev => prev.filter(c => c._id !== candidate._id))
    }
  }

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedCandidates(displayCandidates)
    } else {
      setSelectedCandidates([])
    }
  }

  const isSelected = (candidate: ICandidate) => {
    return selectedCandidates.some(c => c._id === candidate._id)
  }

  const isAllSelected = displayCandidates.length > 0 && selectedCandidates.length === displayCandidates.length

  const getJobTitle = (jobId: string) => {
    const job = jobs?.find(j => j._id === jobId)
    return job ? job.title : 'Unknown Job'
  }

  const getStageColor = (stage: string) => {
    const stageColors: Record<string, string> = {
      'registered': 'bg-blue-100 text-blue-800',
      'veda-review': 'bg-purple-100 text-purple-800',
      'screening': 'bg-yellow-100 text-yellow-800',
      'assessment': 'bg-orange-100 text-orange-800',
      'interview': 'bg-indigo-100 text-indigo-800',
      'completed_success': 'bg-green-100 text-green-800',
      'completed_fail': 'bg-red-100 text-red-800',
      'workflow_terminated': 'bg-gray-100 text-gray-800',
    }
    return stageColors[stage] || 'bg-gray-100 text-gray-800'
  }

  const getStatusColor = (status: string) => {
    const statusColors: Record<string, string> = {
      'registered': 'bg-blue-100 text-blue-800',
      'pending_schedule': 'bg-yellow-100 text-yellow-800',
      'queued': 'bg-purple-100 text-purple-800',
      'in_progress': 'bg-orange-100 text-orange-800',
      'awaiting_result': 'bg-indigo-100 text-indigo-800',
      'completed_success': 'bg-green-100 text-green-800',
      'completed_fail': 'bg-red-100 text-red-800',
      'workflow_terminated': 'bg-gray-100 text-gray-800',
      'error': 'bg-red-100 text-red-800',
    }
    return statusColors[status] || 'bg-gray-100 text-gray-800'
  }

  const columns = [
    // {
    //   key: 'select',
    //   label: (
    //     <Checkbox
    //       checked={isAllSelected}
    //       onCheckedChange={handleSelectAll}
    //       aria-label="Select all candidates"
    //     />
    //   ),
    //   render: (_: any, row: ICandidate) => (
    //     <Checkbox
    //       checked={isSelected(row)}
    //       onCheckedChange={(checked) => handleSelectCandidate(row, checked as boolean)}
    //       aria-label={`Select ${row.name}`}
    //     />
    //   )
    // },
    {
      key: 'name',
      label: 'Candidate',
      sortable: true,
      render: (value: string, row: ICandidate) => (
        <div className="flex items-center space-x-3 min-w-[250px]">
          <div className="flex-shrink-0">
            <div className="h-10 w-10 rounded-full bg-gradient-to-br from-blue-400 to-blue-600 flex items-center justify-center">
              <User className="h-5 w-5 text-white" />
            </div>
          </div>
          <div className="min-w-0 flex-1">
            <div className="text-sm font-semibold text-gray-900 truncate">{value}</div>
            <div className="text-xs text-gray-500 flex items-center space-x-1 mt-1">
              <Mail className="h-3 w-3 flex-shrink-0" />
              <span className="truncate">{row.email}</span>
            </div>
            <div className="text-xs text-gray-500 flex items-center space-x-1 mt-0.5">
              <Phone className="h-3 w-3 flex-shrink-0" />
              <span>{row.phone}</span>
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'jobId',
      label: 'Applied Job',
      sortable: true,
      render: (value: string) => (
        <div className="min-w-[180px]">
          <div className="flex items-center space-x-2">
            <Briefcase className="h-4 w-4 text-blue-500 flex-shrink-0" />
            <span className="text-sm font-medium text-gray-900 truncate">{getJobTitle(value)}</span>
          </div>
        </div>
      )
    },
    {
      key: 'stage',
      label: 'Current Stage',
      sortable: true,
      render: (value: string, row: ICandidate) => (
        <div className="min-w-[160px]">
          <CandidateStageManager candidate={row} compact />
        </div>
      )
    },
    {
      key: 'status',
      label: 'Status',
      sortable: true,
      render: (value: string) => (
        <div className="min-w-[120px]">
          <Badge className={`${getStatusColor(value)} border-0 text-xs px-2 py-1`}>
            {value.replace('_', ' ').toUpperCase()}
          </Badge>
        </div>
      )
    },
    {
      key: 'source',
      label: 'Source',
      sortable: true,
      render: (value: string) => (
        <div className="min-w-[100px]">
          <span className="text-sm text-gray-600 font-medium">{value}</span>
        </div>
      )
    },
    {
      key: 'expectedSalary',
      label: 'Expected Salary',
      sortable: true,
      render: (value: number | undefined) => (
        <div className="min-w-[130px]">
          <div className="flex items-center space-x-1">
            <DollarSign className="h-4 w-4 text-green-500 flex-shrink-0" />
            <span className="text-sm font-semibold text-gray-900">
              {value ? `$${(value / 1000).toFixed(0)}k` : 'N/A'}
            </span>
          </div>
        </div>
      )
    },
    {
      key: 'createdAt',
      label: 'Applied Date',
      sortable: true,
      render: (value: Date | string) => (
        <div className="min-w-[120px]">
          <div className="flex items-center space-x-1">
            <Clock className="h-4 w-4 text-gray-400 flex-shrink-0" />
            <span className="text-sm text-gray-600">{formatDate(value)}</span>
          </div>
        </div>
      )
    },
  ]

  if (viewingCandidate) {
    return (
      <CandidateDetail
        candidate={viewingCandidate}
        onBack={() => setViewingCandidate(null)}
        onEdit={(candidate: ICandidate) => {
          setViewingCandidate(null)
          setEditingCandidate(candidate)
        }}
        onDelete={handleDelete}
      />
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Candidates</h1>
          <p className="text-gray-600">Manage candidate applications and track their progress</p>
        </div>
        <div className="flex items-center space-x-3">
          {/* CSV Actions */}
          <Button
            variant="outline"
            onClick={handleDownloadTemplate}
            disabled={downloadTemplateMutation.isPending}
          >
            <Download className="h-4 w-4 mr-2" />
            Download Template
          </Button>
          <Button
            variant="outline"
            onClick={() => setIsCSVUploadOpen(true)}
          >
            <Upload className="h-4 w-4 mr-2" />
            Upload CSV
          </Button>

          {/* Add Candidate */}
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Add Candidate
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Add New Candidate</DialogTitle>
              </DialogHeader>
              <CandidateForm
                mode="create"
                onSubmit={handleCreate}
                onCancel={() => setIsCreateDialogOpen(false)}
                isLoading={createMutation.isPending}
              />
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Search and Filter */}
      <CandidateSearchFilter
        onResultsChange={setFilteredCandidates}
        className="bg-white p-4 rounded-lg shadow-sm"
      />

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {filteredCandidates ? 'Filtered' : 'Total'} Candidates
            </CardTitle>
            <User className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{displayCandidates.length}</div>
            {filteredCandidates && (
              <p className="text-xs text-muted-foreground">
                of {candidates?.length || 0} total
              </p>
            )}
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">In Progress</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {candidates?.filter(c => c.status === 'in_progress').length || 0}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Successful</CardTitle>
            <User className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {candidates?.filter(c => c.status === 'completed_success').length || 0}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg. Expected Salary</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              ${Math.round(candidates?.filter(c => c.expectedSalary)
                .reduce((sum, c) => sum + (c.expectedSalary || 0), 0) /
                (candidates?.filter(c => c.expectedSalary).length || 1) || 0).toLocaleString()}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Bulk Stage Manager */}
      {/* <BulkStageManager
        selectedCandidates={selectedCandidates}
        onSelectionChange={setSelectedCandidates}
      /> */}

      <div className="bg-white rounded-lg shadow-sm">
        <DataTable
          data={displayCandidates}
          columns={columns}
          loading={isLoading}
          onEdit={(id) => {
            const candidate = displayCandidates.find(c => c._id === id)
            if (candidate) setEditingCandidate(candidate)
          }}
          onDelete={handleDelete}
          onView={(id) => {
            const candidate = displayCandidates.find(c => c._id === id)
            if (candidate) setViewingCandidate(candidate)
          }}
        />
      </div>

      {/* Edit Dialog */}
      <Dialog open={!!editingCandidate} onOpenChange={() => setEditingCandidate(null)}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Candidate</DialogTitle>
          </DialogHeader>
          {editingCandidate && (
            <CandidateForm
              mode="edit"
              initialData={editingCandidate}
              onSubmit={handleUpdate}
              onCancel={() => setEditingCandidate(null)}
              isLoading={updateMutation.isPending}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* CSV Upload Dialog */}
      <CSVUpload
        isOpen={isCSVUploadOpen}
        onClose={() => setIsCSVUploadOpen(false)}
        onSuccess={handleCSVUploadSuccess}
      />
    </div>
  )
}
