import { memo } from 'react'
import { <PERSON><PERSON>, <PERSON>si<PERSON>, type NodeProps } from 'reactflow'
import { <PERSON><PERSON>s, CheckCircle, AlertCircle } from 'lucide-react'
import { cn } from '@/lib/utils'
import { useWorkflowStore, type WorkflowStageData } from '@/stores/workflowStore'

interface WorkflowStageNodeProps extends NodeProps {
  data: WorkflowStageData
}

export const WorkflowStageNode = memo(({ data, selected }: WorkflowStageNodeProps) => {
  const { selectNode, getNodeById } = useWorkflowStore()
  
  const handleClick = () => {
    const node = getNodeById(data.stage)
    if (node) {
      selectNode(node)
    }
  }

  const getStageIcon = () => {
    if (data.isConfigured) {
      return <CheckCircle className="h-4 w-4 text-green-400" />
    }
    return <AlertCircle className="h-4 w-4 text-yellow-400" />
  }

  const getStageColor = () => {
    switch (data.stage) {
      case 'veda-review':
        return 'from-purple-500 to-purple-600'
      case 'screening':
        return 'from-amber-500 to-amber-600'
      case 'assessment':
        return 'from-red-500 to-red-600'
      case 'interview':
        return 'from-green-500 to-green-600'
      case 'final-review':
        return 'from-blue-500 to-blue-600'
      case 'offer':
        return 'from-indigo-500 to-indigo-600'
      case 'rejected':
        return 'from-gray-500 to-gray-600'
      case 'hired':
        return 'from-emerald-500 to-emerald-600'
      default:
        return 'from-gray-500 to-gray-600'
    }
  }

  return (
    <div className="workflow-stage-node">
      {/* Input Handle */}
      <Handle
        type="target"
        position={Position.Left}
        className="w-3 h-3 !bg-gray-400 border-2 border-white"
        style={{ left: -6 }}
      />
      
      {/* Node Content */}
      <div
        className={cn(
          "relative px-4 py-3 rounded-lg shadow-lg cursor-pointer transition-all duration-200",
          "bg-gradient-to-br text-white min-w-[160px]",
          getStageColor(),
          selected && "ring-2 ring-blue-400 ring-offset-2 ring-offset-white",
          "hover:shadow-xl hover:scale-105"
        )}
        onClick={handleClick}
      >
        {/* Header */}
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center space-x-2">
            {getStageIcon()}
            <span className="text-xs font-medium opacity-90">
              {data.stage.toUpperCase()}
            </span>
          </div>
          <Settings className="h-3 w-3 opacity-70" />
        </div>
        
        {/* Stage Label */}
        <div className="text-sm font-semibold mb-1">
          {data.label}
        </div>
        
        {/* Configuration Status */}
        <div className="text-xs opacity-80">
          {data.isConfigured ? (
            <div className="flex items-center space-x-1">
              <span>Configured</span>
              {data.agentId && (
                <span className="bg-white/20 px-1 rounded text-xs">
                  {data.agentId}
                </span>
              )}
            </div>
          ) : (
            <span className="text-yellow-200">Needs Configuration</span>
          )}
        </div>
        
        {/* Communication Channel Indicator */}
        {data.communicationChannel && (
          <div className="absolute -top-1 -right-1 bg-white text-gray-700 text-xs px-1 py-0.5 rounded-full text-[10px] font-medium">
            {data.communicationChannel}
          </div>
        )}
        
        {/* Outputs Preview */}
        {data.outputs && data.outputs.length > 0 && (
          <div className="mt-2 flex flex-wrap gap-1">
            {data.outputs.slice(0, 2).map((output, index) => (
              <span
                key={index}
                className="bg-white/20 text-xs px-1 py-0.5 rounded text-[10px]"
              >
                {output}
              </span>
            ))}
            {data.outputs.length > 2 && (
              <span className="bg-white/20 text-xs px-1 py-0.5 rounded text-[10px]">
                +{data.outputs.length - 2}
              </span>
            )}
          </div>
        )}
      </div>
      
      {/* Output Handle */}
      <Handle
        type="source"
        position={Position.Right}
        className="w-3 h-3 !bg-gray-400 border-2 border-white"
        style={{ right: -6 }}
      />
    </div>
  )
})

WorkflowStageNode.displayName = 'WorkflowStageNode'
