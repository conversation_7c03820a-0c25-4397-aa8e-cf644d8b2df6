import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import tailwindcss from '@tailwindcss/vite'

import path from "path"


// https://vite.dev/config/
export default defineConfig({
  plugins: [react(), tailwindcss()],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  server: {
    proxy: {
      // Proxy API endpoints to backend - use regex patterns to match dynamic paths
      '^/auth': {
        target: 'http://localhost:8080',
        changeOrigin: true,
      },
      '^/recruiter': {
        target: 'http://localhost:8080',
        changeOrigin: true,
      },
      '^/job(?!/-)': {  // Match /job but not /job-config
        target: 'http://localhost:8080',
        changeOrigin: true,
      },
      '^/candidate': {
        target: 'http://localhost:8080',
        changeOrigin: true,
      },
      '^/organization': {
        target: 'http://localhost:8080',
        changeOrigin: true,
      },
      '^/job-config': {
        target: 'http://localhost:8080',
        changeOrigin: true,
      },
    },
  },
})
